exports.id=506,exports.ids=[506],exports.modules={6137:(e,t,s)=>{Promise.resolve().then(s.bind(s,7816)),Promise.resolve().then(s.t.bind(s,1476,23)),Promise.resolve().then(s.t.bind(s,2704,23))},6086:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,6840,23)),Promise.resolve().then(s.t.bind(s,8771,23)),Promise.resolve().then(s.t.bind(s,3225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,3982,23))},7816:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5344),r=s(3729),n=s(783),i=s.n(n),l=s(1750),o=s(3714),c=s(4513),d=s(8200);let m=()=>{let[e,t]=(0,r.useState)(!1),s=[{name:"Home",href:"/"},{name:"Our Legacy",href:"/legacy"},{name:"Collection",href:"/collection"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}];return a.jsx("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"container-custom",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 group",children:[a.jsx("div",{className:"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg group-hover:scale-105 transition-transform duration-300",children:a.jsx(l.Z,{className:"h-8 w-8 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-serif font-bold text-elegant-800",children:"NAGI JEWELLERS"}),a.jsx("p",{className:"text-xs text-gold-600 font-medium tracking-wider",children:"CRAFTED WITH TRUST SINCE 1999"})]})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[s.map(e=>(0,a.jsxs)(i(),{href:e.href,className:"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 relative group",children:[e.name,a.jsx("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gold-500 transition-all duration-300 group-hover:w-full"})]},e.name)),(0,a.jsxs)(i(),{href:"https://amazon.in/shops/nagijewellers",target:"_blank",rel:"noopener noreferrer",className:"btn-primary flex items-center space-x-2",children:[a.jsx(o.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Visit Amazon Store"})]})]}),a.jsx("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg hover:bg-elegant-100 transition-colors duration-300",children:e?a.jsx(c.Z,{className:"h-6 w-6 text-elegant-700"}):a.jsx(d.Z,{className:"h-6 w-6 text-elegant-700"})})]}),e&&a.jsx("div",{className:"md:hidden py-4 border-t border-elegant-200 animate-fade-in",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-4",children:[s.map(e=>a.jsx(i(),{href:e.href,className:"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 py-2",onClick:()=>t(!1),children:e.name},e.name)),(0,a.jsxs)(i(),{href:"https://amazon.in/shops/nagijewellers",target:"_blank",rel:"noopener noreferrer",className:"btn-primary flex items-center justify-center space-x-2 mt-4",onClick:()=>t(!1),children:[a.jsx(o.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Visit Amazon Store"})]})]})})]})})}},329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>L,metadata:()=>b});var a=s(5036);s(7272);let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\Desktop\NAGI JEWELLERS\components\Header.tsx`),{__esModule:n,$$typeof:i}=r,l=r.default;var o=s(646),c=s.n(o),d=s(146),m=s(6682),h=s(7238),g=s(6198),x=s(5049),p=s(870),u=s(389),f=s(9003),j=s(9608),N=s(9273);let y=()=>{let e=new Date().getFullYear(),t=[{icon:d.Z,text:"26+ Years",subtitle:"of Excellence"},{icon:m.Z,text:"10,000+",subtitle:"Happy Customers"},{icon:h.Z,text:"BIS",subtitle:"Certified"},{icon:g.Z,text:"4.5★",subtitle:"Amazon Rating"}];return(0,a.jsxs)("footer",{className:"bg-elegant-900 text-white",children:[a.jsx("div",{className:"border-b border-elegant-700",children:a.jsx("div",{className:"container-custom py-12",children:a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:t.map((e,t)=>(0,a.jsxs)("div",{className:"text-center group",children:[a.jsx("div",{className:"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:a.jsx(e.icon,{className:"h-8 w-8 text-white"})}),a.jsx("h3",{className:"text-2xl font-bold text-gold-400 mb-1",children:e.text}),a.jsx("p",{className:"text-elegant-300 text-sm",children:e.subtitle})]},t))})})}),a.jsx("div",{className:"container-custom py-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(c(),{href:"/",className:"flex items-center space-x-3 mb-6",children:[a.jsx("div",{className:"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg",children:a.jsx(x.Z,{className:"h-8 w-8 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-serif font-bold",children:"NAGI JEWELLERS"}),a.jsx("p",{className:"text-gold-400 text-sm font-medium tracking-wider",children:"CRAFTED WITH TRUST SINCE 1999"})]})]}),a.jsx("p",{className:"text-elegant-300 mb-6 leading-relaxed",children:"For over 26 years, NAGI JEWELLERS LTD has been crafting beautiful, authentic jewellery with unwavering commitment to quality and trust. From our humble offline beginnings to serving customers nationwide through Amazon, our legacy continues to shine."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx(c(),{href:"https://instagram.com/nagijewellers",target:"_blank",rel:"noopener noreferrer",className:"bg-elegant-800 hover:bg-gold-500 p-3 rounded-lg transition-colors duration-300",children:a.jsx(p.Z,{className:"h-5 w-5"})}),a.jsx(c(),{href:"https://wa.me/919876543210",target:"_blank",rel:"noopener noreferrer",className:"bg-elegant-800 hover:bg-green-500 p-3 rounded-lg transition-colors duration-300",children:a.jsx(u.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-serif font-semibold mb-6 text-gold-400",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[[{name:"Home",href:"/"},{name:"Our Legacy",href:"/legacy"},{name:"Collection",href:"/collection"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}].map(e=>a.jsx("li",{children:a.jsx(c(),{href:e.href,className:"text-elegant-300 hover:text-gold-400 transition-colors duration-300",children:e.name})},e.name)),a.jsx("li",{children:a.jsx(c(),{href:"https://amazon.in/shops/nagijewellers",target:"_blank",rel:"noopener noreferrer",className:"text-elegant-300 hover:text-gold-400 transition-colors duration-300",children:"Amazon Store"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-serif font-semibold mb-6 text-gold-400",children:"Get in Touch"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(f.Z,{className:"h-5 w-5 text-gold-500 mt-1 flex-shrink-0"}),a.jsx("div",{children:(0,a.jsxs)("p",{className:"text-elegant-300 text-sm",children:["123 Jewellery Street",a.jsx("br",{}),"Mumbai, Maharashtra 400001",a.jsx("br",{}),"India"]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(j.Z,{className:"h-5 w-5 text-gold-500 flex-shrink-0"}),a.jsx("p",{className:"text-elegant-300 text-sm",children:"+91 98765 43210"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(N.Z,{className:"h-5 w-5 text-gold-500 flex-shrink-0"}),a.jsx("p",{className:"text-elegant-300 text-sm",children:"<EMAIL>"})]})]})]})]})}),a.jsx("div",{className:"border-t border-elegant-700",children:a.jsx("div",{className:"container-custom py-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("p",{className:"text-elegant-400 text-sm",children:["\xa9 ",e," NAGI JEWELLERS LTD. All rights reserved."]}),(0,a.jsxs)("div",{className:"flex space-x-6 text-sm",children:[a.jsx(c(),{href:"/privacy",className:"text-elegant-400 hover:text-gold-400 transition-colors duration-300",children:"Privacy Policy"}),a.jsx(c(),{href:"/terms",className:"text-elegant-400 hover:text-gold-400 transition-colors duration-300",children:"Terms of Service"})]})]})})})]})};var v=s(4825),w=s.n(v);let E=({type:e,data:t})=>a.jsx(w(),{id:`structured-data-${e}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify((()=>{switch(e){case"organization":return{"@context":"https://schema.org","@type":"Organization",name:"NAGI JEWELLERS LTD",alternateName:"Nagi Jewellers",url:"https://nagijewellers.com",logo:"https://nagijewellers.com/logo.png",description:"NAGI JEWELLERS LTD - 26+ years of trust and tradition in authentic jewellery. Specializing in artificial, silver, and gold-plated jewellery.",foundingDate:"1999",founder:{"@type":"Person",name:"Nagi Family"},address:{"@type":"PostalAddress",streetAddress:"123 Jewellery Street",addressLocality:"Mumbai",addressRegion:"Maharashtra",postalCode:"400001",addressCountry:"IN"},contactPoint:{"@type":"ContactPoint",telephone:"+91-98765-43210",contactType:"customer service",availableLanguage:["English","Hindi"]},sameAs:["https://amazon.in/shops/nagijewellers","https://instagram.com/nagijewellers"],aggregateRating:{"@type":"AggregateRating",ratingValue:"4.5",reviewCount:"1000",bestRating:"5",worstRating:"1"}};case"product":return{"@context":"https://schema.org","@type":"Product",name:t?.name||"NAGI JEWELLERS Collection",description:t?.description||"Authentic jewellery from NAGI JEWELLERS LTD",brand:{"@type":"Brand",name:"NAGI JEWELLERS LTD"},manufacturer:{"@type":"Organization",name:"NAGI JEWELLERS LTD"},category:"Jewellery",offers:{"@type":"Offer",price:t?.price||"999",priceCurrency:"INR",availability:"https://schema.org/InStock",seller:{"@type":"Organization",name:"NAGI JEWELLERS LTD"}},aggregateRating:{"@type":"AggregateRating",ratingValue:"4.5",reviewCount:"100"}};case"review":return{"@context":"https://schema.org","@type":"Review",itemReviewed:{"@type":"Organization",name:"NAGI JEWELLERS LTD"},reviewRating:{"@type":"Rating",ratingValue:t?.rating||"5",bestRating:"5"},author:{"@type":"Person",name:t?.author||"Customer"},reviewBody:t?.review||"Excellent quality and service from NAGI JEWELLERS"};case"breadcrumb":return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:t?.breadcrumbs?.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url}))||[]};default:return{}}})())}}),b={title:"NAGI JEWELLERS LTD - Crafted with Trust Since 1999",description:"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship. Shop our collection of artificial, silver, and gold-plated jewellery on Amazon.",keywords:"jewellery, nagi jewellers, gold plated, silver jewellery, artificial jewellery, amazon seller, trust, legacy, authentic jewellery",authors:[{name:"NAGI JEWELLERS LTD"}],creator:"NAGI JEWELLERS LTD",publisher:"NAGI JEWELLERS LTD",openGraph:{title:"NAGI JEWELLERS LTD - Crafted with Trust Since 1999",description:"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.",url:"https://nagijewellers.com",siteName:"NAGI JEWELLERS LTD",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"NAGI JEWELLERS LTD - Premium Jewellery Collection"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"NAGI JEWELLERS LTD - Crafted with Trust Since 1999",description:"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function L({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),a.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),a.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),a.jsx("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,a.jsxs)("body",{className:"min-h-screen bg-white text-elegant-800",children:[a.jsx(E,{type:"organization"}),a.jsx(l,{}),a.jsx("main",{className:"min-h-screen",children:e}),a.jsx(y,{})]})]})}},7272:()=>{}};