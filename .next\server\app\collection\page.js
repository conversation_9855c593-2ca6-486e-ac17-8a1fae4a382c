/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/collection/page";
exports.ids = ["app/collection/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcollection%2Fpage&page=%2Fcollection%2Fpage&appPaths=%2Fcollection%2Fpage&pagePath=private-next-app-dir%2Fcollection%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcollection%2Fpage&page=%2Fcollection%2Fpage&appPaths=%2Fcollection%2Fpage&pagePath=private-next-app-dir%2Fcollection%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'collection',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/collection/page.tsx */ \"(rsc)/./app/collection/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/collection/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/collection/page\",\n        pathname: \"/collection\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcollection%2Fpage&page=%2Fcollection%2Fpage&appPaths=%2Fcollection%2Fpage&pagePath=private-next-app-dir%2Fcollection%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTmFtYW4lMjBOYWdpJTVDRGVza3RvcCU1Q05BR0klMjBKRVdFTExFUlMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDY29tcG9uZW50cyU1Q0hlYWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNOYW1hbiUyME5hZ2klNUNEZXNrdG9wJTVDTkFHSSUyMEpFV0VMTEVSUyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNzY3JpcHQuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUEyRztBQUMzRyxnTUFBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLz8zYjczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE5hbWFuIE5hZ2lcXFxcRGVza3RvcFxcXFxOQUdJIEpFV0VMTEVSU1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxzY3JpcHQuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5Ccollection%5CCategoryFilter.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5Ccollection%5CCategoryFilter.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/collection/CategoryFilter.tsx */ \"(ssr)/./components/collection/CategoryFilter.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTmFtYW4lMjBOYWdpJTVDRGVza3RvcCU1Q05BR0klMjBKRVdFTExFUlMlNUNjb21wb25lbnRzJTVDY29sbGVjdGlvbiU1Q0NhdGVnb3J5RmlsdGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNpbWFnZS1jb21wb25lbnQuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNOYW1hbiUyME5hZ2klNUNEZXNrdG9wJTVDTkFHSSUyMEpFV0VMTEVSUyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQStIO0FBQy9ILHNOQUF5STtBQUN6SSIsInNvdXJjZXMiOlsid2VicGFjazovL25hZ2ktamV3ZWxsZXJzLXdlYnNpdGUvPzVmMWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxOYW1hbiBOYWdpXFxcXERlc2t0b3BcXFxcTkFHSSBKRVdFTExFUlNcXFxcY29tcG9uZW50c1xcXFxjb2xsZWN0aW9uXFxcXENhdGVnb3J5RmlsdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5Ccollection%5CCategoryFilter.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg group-hover:scale-105 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-serif font-bold text-elegant-800\",\n                                            children: \"NAGI JEWELLERS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gold-600 font-medium tracking-wider\",\n                                            children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 relative group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gold-500 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://amazon.in/shops/nagijewellers\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Visit Amazon Store\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg hover:bg-elegant-100 transition-colors duration-300\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-elegant-200 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 py-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"https://amazon.in/shops/nagijewellers\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary flex items-center justify-center space-x-2 mt-4\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Visit Amazon Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/collection/CategoryFilter.tsx":
/*!**************************************************!*\
  !*** ./components/collection/CategoryFilter.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Gem,Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Gem,Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Gem,Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Gem,Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gem.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CategoryFilter = ()=>{\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All Collections\",\n            icon: _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            count: \"150+ Items\",\n            description: \"Complete range of our jewellery\"\n        },\n        {\n            id: \"artificial\",\n            name: \"Artificial Jewellery\",\n            icon: _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            count: \"80+ Items\",\n            description: \"Trendy and affordable designs\"\n        },\n        {\n            id: \"silver\",\n            name: \"Silver Collection\",\n            icon: _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            count: \"45+ Items\",\n            description: \"Pure silver with elegant finish\"\n        },\n        {\n            id: \"gold-plated\",\n            name: \"Gold-Plated\",\n            icon: _barrel_optimize_names_Crown_Gem_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            count: \"35+ Items\",\n            description: \"Luxurious gold-plated pieces\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-elegant-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-serif font-bold text-elegant-800 mb-4\",\n                            children: [\n                                \"Browse by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 23\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-elegant-600 max-w-2xl mx-auto\",\n                            children: \"Choose from our carefully curated categories to find the perfect piece for every occasion\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveCategory(category.id),\n                            className: `group relative overflow-hidden rounded-2xl p-6 text-left transition-all duration-300 transform hover:-translate-y-1 ${activeCategory === category.id ? \"bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-2xl scale-105\" : \"bg-white text-elegant-800 shadow-lg hover:shadow-xl\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-current to-transparent rounded-full blur-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-current to-transparent rounded-full blur-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `inline-flex items-center justify-center w-12 h-12 rounded-full mb-4 transition-all duration-300 ${activeCategory === category.id ? \"bg-white/20 text-white\" : \"bg-gold-500 text-white group-hover:scale-110\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: `text-lg font-semibold mb-2 transition-colors duration-300 ${activeCategory === category.id ? \"text-white\" : \"text-elegant-800 group-hover:text-gold-600\"}`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-sm mb-3 transition-colors duration-300 ${activeCategory === category.id ? \"text-gold-100\" : \"text-elegant-600\"}`,\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-xs font-medium transition-colors duration-300 ${activeCategory === category.id ? \"text-gold-200\" : \"text-gold-600\"}`,\n                                            children: category.count\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        activeCategory === category.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-white rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto\",\n                        children: [\n                            activeCategory === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-serif font-bold text-elegant-800 mb-4\",\n                                        children: \"Complete Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-600 leading-relaxed\",\n                                        children: \"Explore our entire range of authentic jewellery pieces. From everyday wear to special occasions, find the perfect piece that reflects your style and personality. Each item is crafted with 26+ years of expertise and comes with our guarantee of authenticity.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, undefined),\n                            activeCategory === \"artificial\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-serif font-bold text-elegant-800 mb-4\",\n                                        children: \"Artificial Jewellery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-600 leading-relaxed\",\n                                        children: \"Discover trendy and affordable artificial jewellery that doesn't compromise on style. Perfect for daily wear, parties, and gifting. Our artificial collection features contemporary designs that keep you fashionable without breaking the bank.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined),\n                            activeCategory === \"silver\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-serif font-bold text-elegant-800 mb-4\",\n                                        children: \"Silver Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-600 leading-relaxed\",\n                                        children: \"Elegant silver jewellery crafted from pure silver with exquisite finishing. Our silver collection combines traditional craftsmanship with modern designs, perfect for both casual and formal occasions. Each piece is hallmarked for purity.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined),\n                            activeCategory === \"gold-plated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-serif font-bold text-elegant-800 mb-4\",\n                                        children: \"Gold-Plated Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-600 leading-relaxed\",\n                                        children: \"Experience the luxury of gold with our premium gold-plated collection. These pieces offer the rich appearance of gold jewelry at accessible prices. Perfect for weddings, festivals, and special celebrations with lasting quality and shine.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CategoryFilter.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryFilter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/collection/CategoryFilter.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce7732edcea2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzPzBkMjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjZTc3MzJlZGNlYTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/collection/page.tsx":
/*!*********************************!*\
  !*** ./app/collection/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_collection_CollectionHero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/collection/CollectionHero */ \"(rsc)/./components/collection/CollectionHero.tsx\");\n/* harmony import */ var _components_collection_CategoryFilter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/collection/CategoryFilter */ \"(rsc)/./components/collection/CategoryFilter.tsx\");\n/* harmony import */ var _components_collection_ProductGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/collection/ProductGrid */ \"(rsc)/./components/collection/ProductGrid.tsx\");\n/* harmony import */ var _components_collection_CollectionCTA__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/collection/CollectionCTA */ \"(rsc)/./components/collection/CollectionCTA.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Jewellery Collection - NAGI JEWELLERS LTD | Artificial, Silver & Gold-Plated\",\n    description: \"Explore our exquisite collection of artificial, silver, and gold-plated jewellery. Shop authentic designs on Amazon with 26+ years of trust and quality.\",\n    keywords: \"jewellery collection, artificial jewellery, silver jewellery, gold plated jewellery, nagi jewellers, amazon jewellery, authentic designs\"\n};\nfunction CollectionPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collection_CollectionHero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collection_CategoryFilter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collection_ProductGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collection_CollectionCTA__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\collection\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/collection/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_StructuredData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/StructuredData */ \"(rsc)/./components/StructuredData.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n    description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship. Shop our collection of artificial, silver, and gold-plated jewellery on Amazon.\",\n    keywords: \"jewellery, nagi jewellers, gold plated, silver jewellery, artificial jewellery, amazon seller, trust, legacy, authentic jewellery\",\n    authors: [\n        {\n            name: \"NAGI JEWELLERS LTD\"\n        }\n    ],\n    creator: \"NAGI JEWELLERS LTD\",\n    publisher: \"NAGI JEWELLERS LTD\",\n    openGraph: {\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        url: \"https://nagijewellers.com\",\n        siteName: \"NAGI JEWELLERS LTD\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"NAGI JEWELLERS LTD - Premium Jewellery Collection\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-white text-elegant-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StructuredData__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        type: \"organization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const quickLinks = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const trustBadges = [\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            text: \"26+ Years\",\n            subtitle: \"of Excellence\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            text: \"10,000+\",\n            subtitle: \"Happy Customers\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: \"BIS\",\n            subtitle: \"Certified\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: \"4.5★\",\n            subtitle: \"Amazon Rating\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-elegant-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: trustBadges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(badge.icon, {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gold-400 mb-1\",\n                                        children: badge.text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-300 text-sm\",\n                                        children: badge.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-serif font-bold\",\n                                                    children: \"NAGI JEWELLERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gold-400 text-sm font-medium tracking-wider\",\n                                                    children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300 mb-6 leading-relaxed\",\n                                    children: \"For over 26 years, NAGI JEWELLERS LTD has been crafting beautiful, authentic jewellery with unwavering commitment to quality and trust. From our humble offline beginnings to serving customers nationwide through Amazon, our legacy continues to shine.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://instagram.com/nagijewellers\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-gold-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://wa.me/919876543210\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-green-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"https://amazon.in/shops/nagijewellers\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                children: \"Amazon Store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-elegant-300 text-sm\",\n                                                        children: [\n                                                            \"123 Jewellery Street\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            \"Mumbai, Maharashtra 400001\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 47\n                                                            }, undefined),\n                                                            \"India\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"+91 98765 43210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-elegant-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" NAGI JEWELLERS LTD. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\NAGI JEWELLERS\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/StructuredData.tsx":
/*!***************************************!*\
  !*** ./components/StructuredData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst StructuredData = ({ type, data })=>{\n    const getStructuredData = ()=>{\n        switch(type){\n            case \"organization\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Organization\",\n                    \"name\": \"NAGI JEWELLERS LTD\",\n                    \"alternateName\": \"Nagi Jewellers\",\n                    \"url\": \"https://nagijewellers.com\",\n                    \"logo\": \"https://nagijewellers.com/logo.png\",\n                    \"description\": \"NAGI JEWELLERS LTD - 26+ years of trust and tradition in authentic jewellery. Specializing in artificial, silver, and gold-plated jewellery.\",\n                    \"foundingDate\": \"1999\",\n                    \"founder\": {\n                        \"@type\": \"Person\",\n                        \"name\": \"Nagi Family\"\n                    },\n                    \"address\": {\n                        \"@type\": \"PostalAddress\",\n                        \"streetAddress\": \"123 Jewellery Street\",\n                        \"addressLocality\": \"Mumbai\",\n                        \"addressRegion\": \"Maharashtra\",\n                        \"postalCode\": \"400001\",\n                        \"addressCountry\": \"IN\"\n                    },\n                    \"contactPoint\": {\n                        \"@type\": \"ContactPoint\",\n                        \"telephone\": \"+91-98765-43210\",\n                        \"contactType\": \"customer service\",\n                        \"availableLanguage\": [\n                            \"English\",\n                            \"Hindi\"\n                        ]\n                    },\n                    \"sameAs\": [\n                        \"https://amazon.in/shops/nagijewellers\",\n                        \"https://instagram.com/nagijewellers\"\n                    ],\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"1000\",\n                        \"bestRating\": \"5\",\n                        \"worstRating\": \"1\"\n                    }\n                };\n            case \"product\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Product\",\n                    \"name\": data?.name || \"NAGI JEWELLERS Collection\",\n                    \"description\": data?.description || \"Authentic jewellery from NAGI JEWELLERS LTD\",\n                    \"brand\": {\n                        \"@type\": \"Brand\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"manufacturer\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"category\": \"Jewellery\",\n                    \"offers\": {\n                        \"@type\": \"Offer\",\n                        \"price\": data?.price || \"999\",\n                        \"priceCurrency\": \"INR\",\n                        \"availability\": \"https://schema.org/InStock\",\n                        \"seller\": {\n                            \"@type\": \"Organization\",\n                            \"name\": \"NAGI JEWELLERS LTD\"\n                        }\n                    },\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"100\"\n                    }\n                };\n            case \"review\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Review\",\n                    \"itemReviewed\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"reviewRating\": {\n                        \"@type\": \"Rating\",\n                        \"ratingValue\": data?.rating || \"5\",\n                        \"bestRating\": \"5\"\n                    },\n                    \"author\": {\n                        \"@type\": \"Person\",\n                        \"name\": data?.author || \"Customer\"\n                    },\n                    \"reviewBody\": data?.review || \"Excellent quality and service from NAGI JEWELLERS\"\n                };\n            case \"breadcrumb\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"BreadcrumbList\",\n                    \"itemListElement\": data?.breadcrumbs?.map((item, index)=>({\n                            \"@type\": \"ListItem\",\n                            \"position\": index + 1,\n                            \"name\": item.name,\n                            \"item\": item.url\n                        })) || []\n                };\n            default:\n                return {};\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\n        id: `structured-data-${type}`,\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(getStructuredData())\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\StructuredData.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StructuredData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./components/collection/CategoryFilter.tsx":
/*!**************************************************!*\
  !*** ./components/collection/CategoryFilter.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\NAGI JEWELLERS\components\collection\CategoryFilter.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/collection/CollectionCTA.tsx":
/*!*************************************************!*\
  !*** ./components/collection/CollectionCTA.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,MessageCircle,ShoppingBag,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,MessageCircle,ShoppingBag,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,MessageCircle,ShoppingBag,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,MessageCircle,ShoppingBag,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,MessageCircle,ShoppingBag,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n\n\n\nconst CollectionCTA = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-r from-elegant-800 to-elegant-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-serif font-bold mb-6\",\n                            children: [\n                                \"Ready to Find Your \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400\",\n                                    children: \"Perfect Piece\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 32\n                                }, undefined),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-elegant-300 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Join thousands of satisfied customers who trust NAGI JEWELLERS for their special moments. Shop our complete collection on Amazon with fast delivery and easy returns.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://amazon.in/shops/nagijewellers\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"bg-gold-500 hover:bg-gold-600 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Shop Full Collection on Amazon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    className: \"bg-transparent border-2 border-white text-white hover:bg-white hover:text-elegant-800 font-semibold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center space-x-3 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Get Personal Assistance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gold-400 mb-2\",\n                                    children: \"4.5★\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300\",\n                                    children: \"Amazon Rating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gold-400 mb-2\",\n                                    children: \"10K+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300\",\n                                    children: \"Happy Customers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gold-400 mb-2\",\n                                    children: \"26+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300\",\n                                    children: \"Years Experience\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gold-400 mb-2\",\n                                    children: \"150+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300\",\n                                    children: \"Unique Designs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-white/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-serif font-bold text-center mb-8 text-gold-400\",\n                            children: \"Why Choose NAGI JEWELLERS?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Authentic Quality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"Every piece is genuine and comes with our guarantee of authenticity. BIS certified for your peace of mind.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Trusted Legacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"26+ years of building trust through consistent quality and exceptional customer service.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Easy Shopping\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"Shop conveniently on Amazon with fast delivery, easy returns, and secure payment options.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Affordable Luxury\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"Premium quality jewellery at accessible prices. Special offers and discounts available regularly.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Personal Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"Get personalized recommendations and assistance from our experienced team.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold mb-3 text-white\",\n                                            children: \"Customer First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300\",\n                                            children: \"Your satisfaction is our priority. We go above and beyond to ensure you're happy with your purchase.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-elegant-300 mb-6\",\n                            children: \"Still have questions? We're here to help!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://wa.me/919876543210\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_MessageCircle_ShoppingBag_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"WhatsApp Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/testimonials\",\n                                    className: \"bg-transparent border border-white text-white hover:bg-white hover:text-elegant-800 font-semibold py-3 px-6 rounded-lg transition-all duration-300\",\n                                    children: \"Read Customer Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 p-6 bg-blue-500/20 border border-blue-400/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-200 mb-2\",\n                            children: \"\\uD83D\\uDCF8 Product Photos Needed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Replace all product images above with:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" High-quality photos of actual NAGI JEWELLERS products. Each product should have multiple angles, close-up details, and lifestyle shots showing the jewellery being worn. Ensure consistent lighting and background for a professional look.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionCTA.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollectionCTA);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/collection/CollectionCTA.tsx\n");

/***/ }),

/***/ "(rsc)/./components/collection/CollectionHero.tsx":
/*!**************************************************!*\
  !*** ./components/collection/CollectionHero.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Sparkles,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Sparkles,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Sparkles,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n\n\n\nconst CollectionHero = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-[60vh] flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        src: \"https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                        alt: \"Beautiful jewellery collection\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/60 to-black/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container-custom text-white text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center space-x-2 bg-gold-500/20 backdrop-blur-sm border border-gold-400/30 rounded-full px-6 py-2 mb-8 animate-fade-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 text-gold-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-200 font-medium\",\n                                    children: \"Curated Collection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-serif font-bold mb-6 animate-slide-up\",\n                            children: [\n                                \"Exquisite\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-gold-300 to-gold-500 bg-clip-text text-transparent\",\n                                    children: \"Jewellery Collection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed animate-slide-up animation-delay-200\",\n                            children: [\n                                \"Discover our handpicked selection of artificial, silver, and gold-plated jewellery.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                    className: \"hidden md:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Each piece crafted with 26+ years of expertise and tradition.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 animate-slide-up animation-delay-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gold-400 mb-2\",\n                                            children: \"Artificial Jewellery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Trendy designs at affordable prices\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gold-400 mb-2\",\n                                            children: \"Silver Collection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Pure silver with elegant craftsmanship\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gold-400 mb-2\",\n                                            children: \"Gold-Plated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Luxurious look with lasting quality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center items-center gap-8 animate-slide-up animation-delay-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-gold-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"4.5★ Rated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-6 w-6 text-gold-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"BIS Certified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-6 w-6 text-gold-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"100% Authentic\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\CollectionHero.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollectionHero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/collection/CollectionHero.tsx\n");

/***/ }),

/***/ "(rsc)/./components/collection/ProductGrid.tsx":
/*!***********************************************!*\
  !*** ./components/collection/ProductGrid.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,ShoppingBag,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,ShoppingBag,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,ShoppingBag,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,ShoppingBag,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n\n\n\nconst ProductGrid = ()=>{\n    const products = [\n        // Artificial Jewellery\n        {\n            id: 1,\n            name: \"Elegant Kundan Necklace Set\",\n            category: \"Artificial\",\n            price: \"₹899\",\n            originalPrice: \"₹1,499\",\n            rating: 4.4,\n            reviews: 89,\n            image: \"https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08XXXXX1\",\n            badge: \"Bestseller\",\n            type: \"artificial\"\n        },\n        {\n            id: 2,\n            name: \"Traditional Jhumka Earrings\",\n            category: \"Artificial\",\n            price: \"₹599\",\n            originalPrice: \"₹999\",\n            rating: 4.6,\n            reviews: 156,\n            image: \"https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08XXXXX2\",\n            badge: \"New\",\n            type: \"artificial\"\n        },\n        {\n            id: 3,\n            name: \"Designer Bracelet Set\",\n            category: \"Artificial\",\n            price: \"₹749\",\n            originalPrice: \"₹1,299\",\n            rating: 4.3,\n            reviews: 67,\n            image: \"https://images.unsplash.com/photo-1611652022419-a9419f74343d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08XXXXX3\",\n            badge: \"Trending\",\n            type: \"artificial\"\n        },\n        {\n            id: 4,\n            name: \"Pearl Drop Earrings\",\n            category: \"Artificial\",\n            price: \"₹449\",\n            originalPrice: \"₹799\",\n            rating: 4.5,\n            reviews: 123,\n            image: \"https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08XXXXX4\",\n            badge: \"Popular\",\n            type: \"artificial\"\n        },\n        // Silver Collection\n        {\n            id: 5,\n            name: \"Sterling Silver Chain Necklace\",\n            category: \"Silver\",\n            price: \"₹1,899\",\n            originalPrice: \"₹2,999\",\n            rating: 4.8,\n            reviews: 234,\n            image: \"https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08YYYY1\",\n            badge: \"Premium\",\n            type: \"silver\"\n        },\n        {\n            id: 6,\n            name: \"Silver Oxidized Bangles\",\n            category: \"Silver\",\n            price: \"₹1,299\",\n            originalPrice: \"₹1,999\",\n            rating: 4.7,\n            reviews: 178,\n            image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08YYYY2\",\n            badge: \"Handcrafted\",\n            type: \"silver\"\n        },\n        {\n            id: 7,\n            name: \"Silver Temple Jewelry Set\",\n            category: \"Silver\",\n            price: \"₹2,499\",\n            originalPrice: \"₹3,999\",\n            rating: 4.9,\n            reviews: 89,\n            image: \"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08YYYY3\",\n            badge: \"Traditional\",\n            type: \"silver\"\n        },\n        {\n            id: 8,\n            name: \"Silver Stud Earrings\",\n            category: \"Silver\",\n            price: \"₹899\",\n            originalPrice: \"₹1,399\",\n            rating: 4.6,\n            reviews: 145,\n            image: \"https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08YYYY4\",\n            badge: \"Elegant\",\n            type: \"silver\"\n        },\n        // Gold-Plated Collection\n        {\n            id: 9,\n            name: \"Gold-Plated Bridal Set\",\n            category: \"Gold-Plated\",\n            price: \"₹3,499\",\n            originalPrice: \"₹5,999\",\n            rating: 4.7,\n            reviews: 312,\n            image: \"https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08ZZZZ1\",\n            badge: \"Bridal\",\n            type: \"gold-plated\"\n        },\n        {\n            id: 10,\n            name: \"Gold-Plated Chain with Pendant\",\n            category: \"Gold-Plated\",\n            price: \"₹1,999\",\n            originalPrice: \"₹3,299\",\n            rating: 4.5,\n            reviews: 198,\n            image: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08ZZZZ2\",\n            badge: \"Classic\",\n            type: \"gold-plated\"\n        },\n        {\n            id: 11,\n            name: \"Gold-Plated Bangles Set\",\n            category: \"Gold-Plated\",\n            price: \"₹2,299\",\n            originalPrice: \"₹3,799\",\n            rating: 4.6,\n            reviews: 167,\n            image: \"https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08ZZZZ3\",\n            badge: \"Festive\",\n            type: \"gold-plated\"\n        },\n        {\n            id: 12,\n            name: \"Gold-Plated Ring Collection\",\n            category: \"Gold-Plated\",\n            price: \"₹1,299\",\n            originalPrice: \"₹2,199\",\n            rating: 4.4,\n            reviews: 134,\n            image: \"https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\",\n            amazonUrl: \"https://amazon.in/dp/B08ZZZZ4\",\n            badge: \"Trendy\",\n            type: \"gold-plated\"\n        }\n    ];\n    const getBadgeColor = (badge)=>{\n        const colors = {\n            \"Bestseller\": \"bg-red-500\",\n            \"New\": \"bg-green-500\",\n            \"Trending\": \"bg-purple-500\",\n            \"Popular\": \"bg-blue-500\",\n            \"Premium\": \"bg-gold-500\",\n            \"Handcrafted\": \"bg-orange-500\",\n            \"Traditional\": \"bg-indigo-500\",\n            \"Elegant\": \"bg-pink-500\",\n            \"Bridal\": \"bg-rose-500\",\n            \"Classic\": \"bg-amber-500\",\n            \"Festive\": \"bg-emerald-500\",\n            \"Trendy\": \"bg-cyan-500\"\n        };\n        return colors[badge] || \"bg-gray-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-serif font-bold text-elegant-800 mb-4\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Complete Collection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-elegant-600 max-w-2xl mx-auto\",\n                            children: \"Browse through our carefully curated selection of authentic jewellery pieces, each crafted with precision and backed by 26+ years of trust.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-elegant overflow-hidden group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            src: product.image,\n                                            alt: product.name,\n                                            width: 400,\n                                            height: 400,\n                                            className: \"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `${getBadgeColor(product.badge)} text-white px-3 py-1 rounded-full text-sm font-semibold`,\n                                                children: product.badge\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"absolute top-4 right-4 bg-white/80 hover:bg-white p-2 rounded-full transition-colors duration-300 opacity-0 group-hover:opacity-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 text-elegant-600 hover:text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: product.amazonUrl,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"bg-white text-elegant-800 px-6 py-3 rounded-lg font-semibold hover:bg-gold-500 hover:text-white transition-colors duration-300 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View on Amazon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-600 text-sm font-medium uppercase tracking-wide\",\n                                                children: product.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-elegant-800 mb-3 line-clamp-2 group-hover:text-gold-600 transition-colors duration-300\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gold-500 fill-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-elegant-700 ml-1\",\n                                                            children: product.rating\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-elegant-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.reviews,\n                                                        \" reviews)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-elegant-800\",\n                                                    children: product.price\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-elegant-500 line-through\",\n                                                    children: product.originalPrice\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 font-medium\",\n                                                    children: [\n                                                        \"Save \",\n                                                        Math.round((1 - parseInt(product.price.replace(\"₹\", \"\").replace(\",\", \"\")) / parseInt(product.originalPrice.replace(\"₹\", \"\").replace(\",\", \"\"))) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: product.amazonUrl,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"w-full btn-primary flex items-center justify-center space-x-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_ShoppingBag_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Buy on Amazon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"btn-secondary text-lg px-8 py-4\",\n                        children: \"Load More Products\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\collection\\\\ProductGrid.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/collection/ProductGrid.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcollection%2Fpage&page=%2Fcollection%2Fpage&appPaths=%2Fcollection%2Fpage&pagePath=private-next-app-dir%2Fcollection%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();