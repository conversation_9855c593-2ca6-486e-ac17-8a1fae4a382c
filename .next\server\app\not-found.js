/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTmFtYW4lMjBOYWdpJTVDRGVza3RvcCU1Q05BR0klMjBKRVdFTExFUlMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDY29tcG9uZW50cyU1Q0hlYWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNOYW1hbiUyME5hZ2klNUNEZXNrdG9wJTVDTkFHSSUyMEpFV0VMTEVSUyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNzY3JpcHQuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUEyRztBQUMzRyxnTUFBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLz8zYjczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE5hbWFuIE5hZ2lcXFxcRGVza3RvcFxcXFxOQUdJIEpFV0VMTEVSU1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxzY3JpcHQuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg group-hover:scale-105 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-serif font-bold text-elegant-800\",\n                                            children: \"NAGI JEWELLERS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gold-600 font-medium tracking-wider\",\n                                            children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 relative group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gold-500 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://amazon.in/shops/nagijewellers\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Visit Amazon Store\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg hover:bg-elegant-100 transition-colors duration-300\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-elegant-200 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 py-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"https://amazon.in/shops/nagijewellers\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary flex items-center justify-center space-x-2 mt-4\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Visit Amazon Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce7732edcea2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzPzBkMjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjZTc3MzJlZGNlYTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_StructuredData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/StructuredData */ \"(rsc)/./components/StructuredData.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n    description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship. Shop our collection of artificial, silver, and gold-plated jewellery on Amazon.\",\n    keywords: \"jewellery, nagi jewellers, gold plated, silver jewellery, artificial jewellery, amazon seller, trust, legacy, authentic jewellery\",\n    authors: [\n        {\n            name: \"NAGI JEWELLERS LTD\"\n        }\n    ],\n    creator: \"NAGI JEWELLERS LTD\",\n    publisher: \"NAGI JEWELLERS LTD\",\n    openGraph: {\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        url: \"https://nagijewellers.com\",\n        siteName: \"NAGI JEWELLERS LTD\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"NAGI JEWELLERS LTD - Premium Jewellery Collection\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-white text-elegant-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StructuredData__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        type: \"organization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const quickLinks = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const trustBadges = [\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            text: \"26+ Years\",\n            subtitle: \"of Excellence\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            text: \"10,000+\",\n            subtitle: \"Happy Customers\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: \"BIS\",\n            subtitle: \"Certified\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: \"4.5★\",\n            subtitle: \"Amazon Rating\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-elegant-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: trustBadges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(badge.icon, {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gold-400 mb-1\",\n                                        children: badge.text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-300 text-sm\",\n                                        children: badge.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-serif font-bold\",\n                                                    children: \"NAGI JEWELLERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gold-400 text-sm font-medium tracking-wider\",\n                                                    children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300 mb-6 leading-relaxed\",\n                                    children: \"For over 26 years, NAGI JEWELLERS LTD has been crafting beautiful, authentic jewellery with unwavering commitment to quality and trust. From our humble offline beginnings to serving customers nationwide through Amazon, our legacy continues to shine.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://instagram.com/nagijewellers\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-gold-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://wa.me/919876543210\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-green-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"https://amazon.in/shops/nagijewellers\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                children: \"Amazon Store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-elegant-300 text-sm\",\n                                                        children: [\n                                                            \"123 Jewellery Street\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            \"Mumbai, Maharashtra 400001\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 47\n                                                            }, undefined),\n                                                            \"India\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"+91 98765 43210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-elegant-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" NAGI JEWELLERS LTD. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\NAGI JEWELLERS\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/StructuredData.tsx":
/*!***************************************!*\
  !*** ./components/StructuredData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst StructuredData = ({ type, data })=>{\n    const getStructuredData = ()=>{\n        switch(type){\n            case \"organization\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Organization\",\n                    \"name\": \"NAGI JEWELLERS LTD\",\n                    \"alternateName\": \"Nagi Jewellers\",\n                    \"url\": \"https://nagijewellers.com\",\n                    \"logo\": \"https://nagijewellers.com/logo.png\",\n                    \"description\": \"NAGI JEWELLERS LTD - 26+ years of trust and tradition in authentic jewellery. Specializing in artificial, silver, and gold-plated jewellery.\",\n                    \"foundingDate\": \"1999\",\n                    \"founder\": {\n                        \"@type\": \"Person\",\n                        \"name\": \"Nagi Family\"\n                    },\n                    \"address\": {\n                        \"@type\": \"PostalAddress\",\n                        \"streetAddress\": \"123 Jewellery Street\",\n                        \"addressLocality\": \"Mumbai\",\n                        \"addressRegion\": \"Maharashtra\",\n                        \"postalCode\": \"400001\",\n                        \"addressCountry\": \"IN\"\n                    },\n                    \"contactPoint\": {\n                        \"@type\": \"ContactPoint\",\n                        \"telephone\": \"+91-98765-43210\",\n                        \"contactType\": \"customer service\",\n                        \"availableLanguage\": [\n                            \"English\",\n                            \"Hindi\"\n                        ]\n                    },\n                    \"sameAs\": [\n                        \"https://amazon.in/shops/nagijewellers\",\n                        \"https://instagram.com/nagijewellers\"\n                    ],\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"1000\",\n                        \"bestRating\": \"5\",\n                        \"worstRating\": \"1\"\n                    }\n                };\n            case \"product\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Product\",\n                    \"name\": data?.name || \"NAGI JEWELLERS Collection\",\n                    \"description\": data?.description || \"Authentic jewellery from NAGI JEWELLERS LTD\",\n                    \"brand\": {\n                        \"@type\": \"Brand\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"manufacturer\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"category\": \"Jewellery\",\n                    \"offers\": {\n                        \"@type\": \"Offer\",\n                        \"price\": data?.price || \"999\",\n                        \"priceCurrency\": \"INR\",\n                        \"availability\": \"https://schema.org/InStock\",\n                        \"seller\": {\n                            \"@type\": \"Organization\",\n                            \"name\": \"NAGI JEWELLERS LTD\"\n                        }\n                    },\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"100\"\n                    }\n                };\n            case \"review\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Review\",\n                    \"itemReviewed\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"reviewRating\": {\n                        \"@type\": \"Rating\",\n                        \"ratingValue\": data?.rating || \"5\",\n                        \"bestRating\": \"5\"\n                    },\n                    \"author\": {\n                        \"@type\": \"Person\",\n                        \"name\": data?.author || \"Customer\"\n                    },\n                    \"reviewBody\": data?.review || \"Excellent quality and service from NAGI JEWELLERS\"\n                };\n            case \"breadcrumb\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"BreadcrumbList\",\n                    \"itemListElement\": data?.breadcrumbs?.map((item, index)=>({\n                            \"@type\": \"ListItem\",\n                            \"position\": index + 1,\n                            \"name\": item.name,\n                            \"item\": item.url\n                        })) || []\n                };\n            default:\n                return {};\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\n        id: `structured-data-${type}`,\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(getStructuredData())\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\StructuredData.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StructuredData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/StructuredData.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();