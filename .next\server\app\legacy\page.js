/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/legacy/page";
exports.ids = ["app/legacy/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flegacy%2Fpage&page=%2Flegacy%2Fpage&appPaths=%2Flegacy%2Fpage&pagePath=private-next-app-dir%2Flegacy%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flegacy%2Fpage&page=%2Flegacy%2Fpage&appPaths=%2Flegacy%2Fpage&pagePath=private-next-app-dir%2Flegacy%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'legacy',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/legacy/page.tsx */ \"(rsc)/./app/legacy/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/legacy/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/legacy/page\",\n        pathname: \"/legacy\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZsZWdhY3klMkZwYWdlJnBhZ2U9JTJGbGVnYWN5JTJGcGFnZSZhcHBQYXRocz0lMkZsZWdhY3klMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGbGVnYWN5JTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNOYW1hbiUyME5hZ2klNUNEZXNrdG9wJTVDTkFHSSUyMEpFV0VMTEVSUyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1leHBvcnQmcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsc0pBQTBHO0FBQ2pJO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLDRJQUFvRztBQUM3SCxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmFnaS1qZXdlbGxlcnMtd2Vic2l0ZS8/ZmVmMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdsZWdhY3knLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxOYW1hbiBOYWdpXFxcXERlc2t0b3BcXFxcTkFHSSBKRVdFTExFUlNcXFxcYXBwXFxcXGxlZ2FjeVxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxOYW1hbiBOYWdpXFxcXERlc2t0b3BcXFxcTkFHSSBKRVdFTExFUlNcXFxcYXBwXFxcXGxlZ2FjeVxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXE5hbWFuIE5hZ2lcXFxcRGVza3RvcFxcXFxOQUdJIEpFV0VMTEVSU1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXGFwcFxcXFxsZWdhY3lcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9sZWdhY3kvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9sZWdhY3kvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvbGVnYWN5XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flegacy%2Fpage&page=%2Flegacy%2Fpage&appPaths=%2Flegacy%2Fpage&pagePath=private-next-app-dir%2Flegacy%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTmFtYW4lMjBOYWdpJTVDRGVza3RvcCU1Q05BR0klMjBKRVdFTExFUlMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDY29tcG9uZW50cyU1Q0hlYWRlci50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNOYW1hbiUyME5hZ2klNUNEZXNrdG9wJTVDTkFHSSUyMEpFV0VMTEVSUyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q05hbWFuJTIwTmFnaSU1Q0Rlc2t0b3AlNUNOQUdJJTIwSkVXRUxMRVJTJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNzY3JpcHQuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUEyRztBQUMzRyxnTUFBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLz8zYjczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXE5hbWFuIE5hZ2lcXFxcRGVza3RvcFxcXFxOQUdJIEpFV0VMTEVSU1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxzY3JpcHQuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTmFtYW4lMjBOYWdpJTVDRGVza3RvcCU1Q05BR0klMjBKRVdFTExFUlMlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2ltYWdlLWNvbXBvbmVudC5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLz85MzA1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTmFtYW4gTmFnaVxcXFxEZXNrdG9wXFxcXE5BR0kgSkVXRUxMRVJTXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGltYWdlLWNvbXBvbmVudC5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Menu,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg group-hover:scale-105 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-serif font-bold text-elegant-800\",\n                                            children: \"NAGI JEWELLERS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gold-600 font-medium tracking-wider\",\n                                            children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 relative group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gold-500 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"https://amazon.in/shops/nagijewellers\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Visit Amazon Store\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg hover:bg-elegant-100 transition-colors duration-300\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-elegant-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-elegant-200 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-elegant-700 hover:text-gold-600 font-medium transition-colors duration-300 py-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"https://amazon.in/shops/nagijewellers\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary flex items-center justify-center space-x-2 mt-4\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Menu_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Visit Amazon Store\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce7732edcea2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzPzBkMjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjZTc3MzJlZGNlYTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_StructuredData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/StructuredData */ \"(rsc)/./components/StructuredData.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n    description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship. Shop our collection of artificial, silver, and gold-plated jewellery on Amazon.\",\n    keywords: \"jewellery, nagi jewellers, gold plated, silver jewellery, artificial jewellery, amazon seller, trust, legacy, authentic jewellery\",\n    authors: [\n        {\n            name: \"NAGI JEWELLERS LTD\"\n        }\n    ],\n    creator: \"NAGI JEWELLERS LTD\",\n    publisher: \"NAGI JEWELLERS LTD\",\n    openGraph: {\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        url: \"https://nagijewellers.com\",\n        siteName: \"NAGI JEWELLERS LTD\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"NAGI JEWELLERS LTD - Premium Jewellery Collection\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"NAGI JEWELLERS LTD - Crafted with Trust Since 1999\",\n        description: \"Discover authentic jewellery from NAGI JEWELLERS LTD. 26+ years of trust, tradition, and quality craftsmanship.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-white text-elegant-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StructuredData__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        type: \"organization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/legacy/page.tsx":
/*!*****************************!*\
  !*** ./app/legacy/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LegacyPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_legacy_LegacyHero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/legacy/LegacyHero */ \"(rsc)/./components/legacy/LegacyHero.tsx\");\n/* harmony import */ var _components_legacy_Timeline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/legacy/Timeline */ \"(rsc)/./components/legacy/Timeline.tsx\");\n/* harmony import */ var _components_legacy_FounderStory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/legacy/FounderStory */ \"(rsc)/./components/legacy/FounderStory.tsx\");\n/* harmony import */ var _components_legacy_Values__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/legacy/Values */ \"(rsc)/./components/legacy/Values.tsx\");\n/* harmony import */ var _components_legacy_Milestones__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/legacy/Milestones */ \"(rsc)/./components/legacy/Milestones.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Our Legacy - NAGI JEWELLERS LTD | 26+ Years of Trust & Tradition\",\n    description: \"Discover the inspiring journey of NAGI JEWELLERS LTD. From humble beginnings in 1999 to becoming a trusted name in authentic jewellery across India.\",\n    keywords: \"nagi jewellers history, legacy, founder story, jewellery tradition, family business, trust, authentic jewellery\"\n};\nfunction LegacyPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legacy_LegacyHero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legacy_Timeline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legacy_FounderStory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legacy_Values__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_legacy_Milestones__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\app\\\\legacy\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/legacy/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Crown,Instagram,Mail,MapPin,MessageCircle,Phone,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const quickLinks = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Our Legacy\",\n            href: \"/legacy\"\n        },\n        {\n            name: \"Collection\",\n            href: \"/collection\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const trustBadges = [\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            text: \"26+ Years\",\n            subtitle: \"of Excellence\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            text: \"10,000+\",\n            subtitle: \"Happy Customers\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: \"BIS\",\n            subtitle: \"Certified\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: \"4.5★\",\n            subtitle: \"Amazon Rating\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-elegant-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: trustBadges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(badge.icon, {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gold-400 mb-1\",\n                                        children: badge.text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-300 text-sm\",\n                                        children: badge.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-gold-500 to-gold-600 p-2 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-serif font-bold\",\n                                                    children: \"NAGI JEWELLERS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gold-400 text-sm font-medium tracking-wider\",\n                                                    children: \"CRAFTED WITH TRUST SINCE 1999\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-elegant-300 mb-6 leading-relaxed\",\n                                    children: \"For over 26 years, NAGI JEWELLERS LTD has been crafting beautiful, authentic jewellery with unwavering commitment to quality and trust. From our humble offline beginnings to serving customers nationwide through Amazon, our legacy continues to shine.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://instagram.com/nagijewellers\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-gold-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"https://wa.me/919876543210\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"bg-elegant-800 hover:bg-green-500 p-3 rounded-lg transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"https://amazon.in/shops/nagijewellers\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-elegant-300 hover:text-gold-400 transition-colors duration-300\",\n                                                children: \"Amazon Store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-serif font-semibold mb-6 text-gold-400\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 mt-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-elegant-300 text-sm\",\n                                                        children: [\n                                                            \"123 Jewellery Street\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 41\n                                                            }, undefined),\n                                                            \"Mumbai, Maharashtra 400001\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 47\n                                                            }, undefined),\n                                                            \"India\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"+91 98765 43210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Crown_Instagram_Mail_MapPin_MessageCircle_Phone_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gold-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-300 text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-elegant-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-elegant-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" NAGI JEWELLERS LTD. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-elegant-400 hover:text-gold-400 transition-colors duration-300\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\Footer.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\NAGI JEWELLERS\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/StructuredData.tsx":
/*!***************************************!*\
  !*** ./components/StructuredData.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst StructuredData = ({ type, data })=>{\n    const getStructuredData = ()=>{\n        switch(type){\n            case \"organization\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Organization\",\n                    \"name\": \"NAGI JEWELLERS LTD\",\n                    \"alternateName\": \"Nagi Jewellers\",\n                    \"url\": \"https://nagijewellers.com\",\n                    \"logo\": \"https://nagijewellers.com/logo.png\",\n                    \"description\": \"NAGI JEWELLERS LTD - 26+ years of trust and tradition in authentic jewellery. Specializing in artificial, silver, and gold-plated jewellery.\",\n                    \"foundingDate\": \"1999\",\n                    \"founder\": {\n                        \"@type\": \"Person\",\n                        \"name\": \"Nagi Family\"\n                    },\n                    \"address\": {\n                        \"@type\": \"PostalAddress\",\n                        \"streetAddress\": \"123 Jewellery Street\",\n                        \"addressLocality\": \"Mumbai\",\n                        \"addressRegion\": \"Maharashtra\",\n                        \"postalCode\": \"400001\",\n                        \"addressCountry\": \"IN\"\n                    },\n                    \"contactPoint\": {\n                        \"@type\": \"ContactPoint\",\n                        \"telephone\": \"+91-98765-43210\",\n                        \"contactType\": \"customer service\",\n                        \"availableLanguage\": [\n                            \"English\",\n                            \"Hindi\"\n                        ]\n                    },\n                    \"sameAs\": [\n                        \"https://amazon.in/shops/nagijewellers\",\n                        \"https://instagram.com/nagijewellers\"\n                    ],\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"1000\",\n                        \"bestRating\": \"5\",\n                        \"worstRating\": \"1\"\n                    }\n                };\n            case \"product\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Product\",\n                    \"name\": data?.name || \"NAGI JEWELLERS Collection\",\n                    \"description\": data?.description || \"Authentic jewellery from NAGI JEWELLERS LTD\",\n                    \"brand\": {\n                        \"@type\": \"Brand\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"manufacturer\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"category\": \"Jewellery\",\n                    \"offers\": {\n                        \"@type\": \"Offer\",\n                        \"price\": data?.price || \"999\",\n                        \"priceCurrency\": \"INR\",\n                        \"availability\": \"https://schema.org/InStock\",\n                        \"seller\": {\n                            \"@type\": \"Organization\",\n                            \"name\": \"NAGI JEWELLERS LTD\"\n                        }\n                    },\n                    \"aggregateRating\": {\n                        \"@type\": \"AggregateRating\",\n                        \"ratingValue\": \"4.5\",\n                        \"reviewCount\": \"100\"\n                    }\n                };\n            case \"review\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"Review\",\n                    \"itemReviewed\": {\n                        \"@type\": \"Organization\",\n                        \"name\": \"NAGI JEWELLERS LTD\"\n                    },\n                    \"reviewRating\": {\n                        \"@type\": \"Rating\",\n                        \"ratingValue\": data?.rating || \"5\",\n                        \"bestRating\": \"5\"\n                    },\n                    \"author\": {\n                        \"@type\": \"Person\",\n                        \"name\": data?.author || \"Customer\"\n                    },\n                    \"reviewBody\": data?.review || \"Excellent quality and service from NAGI JEWELLERS\"\n                };\n            case \"breadcrumb\":\n                return {\n                    \"@context\": \"https://schema.org\",\n                    \"@type\": \"BreadcrumbList\",\n                    \"itemListElement\": data?.breadcrumbs?.map((item, index)=>({\n                            \"@type\": \"ListItem\",\n                            \"position\": index + 1,\n                            \"name\": item.name,\n                            \"item\": item.url\n                        })) || []\n                };\n            default:\n                return {};\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\n        id: `structured-data-${type}`,\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(getStructuredData())\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\StructuredData.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StructuredData);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./components/legacy/FounderStory.tsx":
/*!********************************************!*\
  !*** ./components/legacy/FounderStory.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,Quote,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,Quote,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,Quote,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Heart,Quote,Star!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n\n\n\nconst FounderStory = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-b from-elegant-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-2xl shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            src: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80\",\n                                            alt: \"Founder of NAGI JEWELLERS - Replace with actual founder photo\",\n                                            width: 600,\n                                            height: 700,\n                                            className: \"w-full h-[500px] object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-8 w-8 text-gold-500 mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-700 italic text-sm leading-relaxed\",\n                                            children: '\"Every piece of jewellery we create carries our family\\'s promise of authenticity and quality.\"'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gold-600 font-semibold mt-3 text-sm\",\n                                            children: \"- Founder, NAGI JEWELLERS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -left-4 w-24 h-24 bg-gold-500/20 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-block bg-gold-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4\",\n                                            children: \"Founder's Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-serif font-bold text-elegant-800 mb-6\",\n                                            children: [\n                                                \"Built on \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gold-600\",\n                                                    children: \"Family Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 26\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 text-lg text-elegant-600 leading-relaxed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"text-elegant-800\",\n                                                    children: \"NAGI JEWELLERS LTD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \" was born from a simple yet powerful vision: to create beautiful, authentic jewellery that celebrates life's most precious moments. What started as a small family business in 1999 has grown into a trusted name across India.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our founder believed that jewellery is more than just an accessory—it's a symbol of love, tradition, and memories that last a lifetime. This philosophy has guided every decision we've made over the past 26 years.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"From sourcing the finest materials to ensuring each piece meets our exacting standards, we've never compromised on quality. Our transition to digital platforms like Amazon has allowed us to share our legacy with families nationwide while maintaining the personal touch that defines us.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-6 mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gold-500 rounded-full p-2 mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-elegant-800 mb-1\",\n                                                            children: \"Family First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-elegant-600\",\n                                                            children: \"Every customer is treated like family\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gold-500 rounded-full p-2 mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-elegant-800 mb-1\",\n                                                            children: \"Quality Promise\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-elegant-600\",\n                                                            children: \"Never compromise on authenticity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gold-500 rounded-full p-2 mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-elegant-800 mb-1\",\n                                                            children: \"Trust & Integrity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-elegant-600\",\n                                                            children: \"Built on honest relationships\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gold-500 rounded-full p-2 mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Heart_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-4 w-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-elegant-800 mb-1\",\n                                                            children: \"Legacy Focus\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-elegant-600\",\n                                                            children: \"Creating memories for generations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gold-50 rounded-lg p-6 mt-8 border-l-4 border-gold-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-700 italic\",\n                                        children: \"\\\"When customers choose NAGI JEWELLERS, they're not just buying jewellery—they're becoming part of our extended family. This responsibility drives us to excel every single day.\\\"\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-800 mb-2\",\n                            children: \"\\uD83D\\uDCF8 Photo Replacement Needed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Replace the founder photo above with:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" An actual photo of the NAGI JEWELLERS founder/family. The current image is a placeholder. Ideal photo should show the founder in the store or with jewellery pieces, conveying trust and authenticity.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\FounderStory.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FounderStory);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/legacy/FounderStory.tsx\n");

/***/ }),

/***/ "(rsc)/./components/legacy/LegacyHero.tsx":
/*!******************************************!*\
  !*** ./components/legacy/LegacyHero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Heart!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Heart!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Heart!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n\n\n\nconst LegacyHero = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-[70vh] flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        src: \"https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                        alt: \"Traditional jewellery craftsmanship\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-black/70 to-black/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container-custom text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center space-x-2 bg-gold-500/20 backdrop-blur-sm border border-gold-400/30 rounded-full px-6 py-2 mb-8 animate-fade-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 text-gold-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-200 font-medium\",\n                                    children: \"Our Story\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-serif font-bold mb-6 animate-slide-up\",\n                            children: [\n                                \"A Legacy of\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-gold-300 to-gold-500 bg-clip-text text-transparent\",\n                                    children: \"Trust & Tradition\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed animate-slide-up animation-delay-200 max-w-3xl\",\n                            children: \"From a small offline store in 1999 to a trusted name across India, discover how NAGI JEWELLERS LTD built a legacy that spans over two decades.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 animate-slide-up animation-delay-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start space-x-3 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gold-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-gold-400\",\n                                                    children: \"1999\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Year Founded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start space-x-3 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gold-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-gold-400\",\n                                                    children: \"26+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Years of Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start space-x-3 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gold-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-gold-400\",\n                                                    children: \"10K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Happy Families\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\LegacyHero.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LegacyHero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/legacy/LegacyHero.tsx\n");

/***/ }),

/***/ "(rsc)/./components/legacy/Milestones.tsx":
/*!******************************************!*\
  !*** ./components/legacy/Milestones.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Heart,Star,Trophy,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n\n\nconst Milestones = ()=>{\n    const achievements = [\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            number: \"26+\",\n            label: \"Years of Excellence\",\n            description: \"Consistently delivering quality since 1999\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            number: \"10,000+\",\n            label: \"Happy Customers\",\n            description: \"Families who trust us for their special moments\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            number: \"4.5★\",\n            label: \"Amazon Rating\",\n            description: \"High customer satisfaction on digital platforms\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            number: \"BIS\",\n            label: \"Certified Quality\",\n            description: \"Meeting highest industry standards\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            number: \"100+\",\n            label: \"Cities Served\",\n            description: \"Nationwide reach through Amazon\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            number: \"3\",\n            label: \"Generations\",\n            description: \"Serving multiple generations of families\"\n        }\n    ];\n    const recognitions = [\n        {\n            title: \"Customer Choice Award\",\n            year: \"2023\",\n            description: \"Recognized for outstanding customer service and product quality\",\n            image: \"https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\"\n        },\n        {\n            title: \"Amazon Top Seller\",\n            year: \"2022\",\n            description: \"Achieved top seller status in jewellery category\",\n            image: \"https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\"\n        },\n        {\n            title: \"Quality Excellence\",\n            year: \"2021\",\n            description: \"BIS certification for maintaining quality standards\",\n            image: \"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-b from-elegant-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-serif font-bold text-elegant-800 mb-6\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Achievements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-elegant-600 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"These milestones represent our journey of growth, trust-building, and commitment to excellence over the past 26 years.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gold-500 to-gold-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(achievement.icon, {\n                                            className: \"h-10 w-10 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-4xl font-bold text-elegant-800 mb-2 group-hover:text-gold-600 transition-colors duration-300\",\n                                        children: achievement.number\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-xl font-semibold text-gold-600 mb-3\",\n                                        children: achievement.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-elegant-600 leading-relaxed\",\n                                        children: achievement.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-serif font-bold text-center text-elegant-800 mb-12\",\n                            children: [\n                                \"Recognition & \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Awards\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: recognitions.map((recognition, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-elegant overflow-hidden group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-48 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: recognition.image,\n                                                    alt: recognition.title,\n                                                    className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gold-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                                        children: recognition.year\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-xl font-semibold text-elegant-800 mb-3\",\n                                                    children: recognition.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-elegant-600 leading-relaxed\",\n                                                    children: recognition.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-gold-500 to-gold-600 rounded-2xl p-8 md:p-12 text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl md:text-4xl font-serif font-bold mb-6\",\n                            children: \"Looking Towards the Future\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-8 text-gold-100 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"As we continue our journey, we remain committed to innovation while preserving the traditional values that have made us who we are. Our goal is to reach even more families and create lasting memories for generations to come.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-2xl font-bold mb-2\",\n                                            children: \"50K+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gold-100\",\n                                            children: \"Customers by 2030\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-2xl font-bold mb-2\",\n                                            children: \"500+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gold-100\",\n                                            children: \"New Designs Annually\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-2xl font-bold mb-2\",\n                                            children: \"100%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gold-100\",\n                                            children: \"Sustainable Practices\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://amazon.in/shops/nagijewellers\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"bg-white text-gold-600 hover:bg-gold-50 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Join Our Journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Globe_Heart_Star_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-800 mb-2\",\n                                children: \"\\uD83D\\uDCF8 Recognition Photos Needed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Replace the recognition images above with:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" Actual photos of awards, certificates, or recognition received by NAGI JEWELLERS. These could include BIS certificates, Amazon seller awards, customer appreciation letters, or newspaper mentions.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Milestones.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Milestones);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/legacy/Milestones.tsx\n");

/***/ }),

/***/ "(rsc)/./components/legacy/Timeline.tsx":
/*!****************************************!*\
  !*** ./components/legacy/Timeline.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Star,Store,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n\n\nconst Timeline = ()=>{\n    const timelineEvents = [\n        {\n            year: \"1999\",\n            title: \"The Beginning\",\n            description: \"NAGI JEWELLERS LTD was founded with a simple vision: to provide authentic, quality jewellery to families. Started as a small offline store with dedication to craftsmanship.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        },\n        {\n            year: \"2005\",\n            title: \"Community Trust\",\n            description: \"Established strong relationships with local communities. Word-of-mouth recommendations became our strongest marketing tool as families trusted us for their special occasions.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        },\n        {\n            year: \"2010\",\n            title: \"Quality Recognition\",\n            description: \"Achieved BIS certification and quality recognition. Expanded our collection to include artificial, silver, and gold-plated jewellery while maintaining authenticity.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        },\n        {\n            year: \"2015\",\n            title: \"Growing Reputation\",\n            description: \"Reached 5,000+ satisfied customers. Our reputation for quality and trust spread beyond local boundaries, attracting customers from neighboring regions.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        },\n        {\n            year: \"2020\",\n            title: \"Digital Transformation\",\n            description: \"Embraced digital platforms and launched on Amazon to serve customers nationwide. Maintained our quality standards while reaching a broader audience.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        },\n        {\n            year: \"2025\",\n            title: \"Legacy Continues\",\n            description: \"Today, with 10,000+ happy customers and 26+ years of experience, we continue to honor our commitment to quality, authenticity, and customer satisfaction.\",\n            icon: _barrel_optimize_names_Award_Calendar_Globe_Star_Store_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            image: \"https://images.unsplash.com/photo-1611652022419-a9419f74343d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-serif font-bold text-elegant-800 mb-6\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-elegant-600 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Every milestone in our journey reflects our unwavering commitment to quality, trust, and customer satisfaction. Here's how we built our legacy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-gold-500 to-gold-600 hidden lg:block\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-16\",\n                            children: timelineEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex flex-col lg:flex-row items-center gap-8 ${index % 2 === 0 ? \"lg:flex-row\" : \"lg:flex-row-reverse\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 lg:max-w-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `card-elegant p-8 ${index % 2 === 0 ? \"lg:text-right\" : \"lg:text-left\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex items-center space-x-3 mb-4 ${index % 2 === 0 ? \"lg:justify-end\" : \"lg:justify-start\"} justify-center`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gold-500 rounded-full p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(event.icon, {\n                                                                    className: \"h-6 w-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                                    lineNumber: 82,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                                lineNumber: 81,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-gold-600\",\n                                                                children: event.year\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                                lineNumber: 84,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-serif font-bold text-elegant-800 mb-4\",\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-elegant-600 leading-relaxed\",\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-gold-500 rounded-full border-4 border-white shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 lg:max-w-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative overflow-hidden rounded-xl shadow-lg group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: event.image,\n                                                        alt: event.title,\n                                                        className: \"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, event.year, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-elegant-50 to-gold-50 rounded-2xl p-8 md:p-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl md:text-4xl font-serif font-bold text-elegant-800 mb-4\",\n                                children: \"The Journey Continues\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-elegant-600 mb-8 max-w-2xl mx-auto\",\n                                children: \"As we look towards the future, our commitment remains unchanged: to provide authentic, quality jewellery that celebrates life's precious moments.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://amazon.in/shops/nagijewellers\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary text-lg px-8 py-4\",\n                                children: \"Be Part of Our Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Timeline.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Timeline);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/legacy/Timeline.tsx\n");

/***/ }),

/***/ "(rsc)/./components/legacy/Values.tsx":
/*!**************************************!*\
  !*** ./components/legacy/Values.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,Heart,Shield,Star,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n\n\nconst Values = ()=>{\n    const coreValues = [\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Authenticity\",\n            description: \"Every piece is genuine and crafted with care. We guarantee the authenticity of all our products.\",\n            color: \"from-red-500 to-pink-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Trust\",\n            description: \"Built over 26 years of honest relationships and transparent business practices.\",\n            color: \"from-blue-500 to-indigo-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Quality\",\n            description: \"BIS certified products that meet the highest standards of craftsmanship and durability.\",\n            color: \"from-gold-500 to-yellow-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Family\",\n            description: \"We treat every customer as family, providing personalized service and care.\",\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Excellence\",\n            description: \"Continuous pursuit of perfection in design, quality, and customer satisfaction.\",\n            color: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: _barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Legacy\",\n            description: \"Creating timeless pieces that become cherished heirlooms for future generations.\",\n            color: \"from-orange-500 to-red-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-serif font-bold text-elegant-800 mb-6\",\n                            children: [\n                                \"Our Core \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-600\",\n                                    children: \"Values\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 22\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-elegant-600 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"These fundamental principles have guided NAGI JEWELLERS LTD for over 26 years and continue to shape every aspect of our business.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: coreValues.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `absolute inset-0 bg-gradient-to-br ${value.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${value.color} mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(value.icon, {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-serif font-bold text-elegant-800 mb-4 group-hover:text-gold-600 transition-colors duration-300\",\n                                            children: value.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-600 leading-relaxed\",\n                                            children: value.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-gold-500/10 to-transparent rounded-full blur-xl group-hover:scale-150 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-elegant-800 to-elegant-900 rounded-2xl p-8 md:p-12 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-3xl md:text-4xl font-serif font-bold mb-4\",\n                                    children: [\n                                        \"Values in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gold-400\",\n                                            children: \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-elegant-300 max-w-2xl mx-auto\",\n                                    children: \"See how our core values translate into real benefits for our customers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"26+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2 text-gold-400\",\n                                            children: \"Years of Trust\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300 text-sm\",\n                                            children: \"Consistent quality and service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"100%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2 text-gold-400\",\n                                            children: \"Authentic Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300 text-sm\",\n                                            children: \"Guaranteed genuine jewellery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"10K+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2 text-gold-400\",\n                                            children: \"Happy Families\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300 text-sm\",\n                                            children: \"Satisfied customers nationwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gold-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"4.5★\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2 text-gold-400\",\n                                            children: \"Amazon Rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-elegant-300 text-sm\",\n                                            children: \"Consistently high reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://amazon.in/shops/nagijewellers\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"bg-gold-500 hover:bg-gold-600 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Experience Our Values\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_Heart_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NAGI JEWELLERS\\\\components\\\\legacy\\\\Values.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Values);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL2xlZ2FjeS9WYWx1ZXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBdUU7QUFFdkUsTUFBTU0sU0FBUztJQUNiLE1BQU1DLGFBQWE7UUFDakI7WUFDRUMsTUFBTVAsK0dBQUtBO1lBQ1hRLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxNQUFNUiwrR0FBTUE7WUFDWlMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtRQUNBO1lBQ0VILE1BQU1OLCtHQUFJQTtZQUNWTyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztRQUNUO1FBQ0E7WUFDRUgsTUFBTUwsK0dBQUtBO1lBQ1hNLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxNQUFNSiwrR0FBS0E7WUFDWEssT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87UUFDVDtRQUNBO1lBQ0VILE1BQU1ILCtHQUFLQTtZQUNYSSxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztRQUNUO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBUUMsV0FBVTtrQkFDakIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUViLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNFOzRCQUFHRixXQUFVOztnQ0FBa0U7OENBQ3JFLDhEQUFDRztvQ0FBS0gsV0FBVTs4Q0FBZ0I7Ozs7Ozs7Ozs7OztzQ0FFM0MsOERBQUNJOzRCQUFFSixXQUFVO3NDQUE2RDs7Ozs7Ozs7Ozs7OzhCQU81RSw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1pOLFdBQVdXLEdBQUcsQ0FBQyxDQUFDQyxPQUFPQyxzQkFDdEIsOERBQUNOOzRCQUVDRCxXQUFVOzs4Q0FHViw4REFBQ0M7b0NBQUlELFdBQVcsQ0FBQyxtQ0FBbUMsRUFBRU0sTUFBTVIsS0FBSyxDQUFDLGlFQUFpRSxDQUFDOzs7Ozs7OENBRXBJLDhEQUFDRztvQ0FBSUQsV0FBVTs7c0RBRWIsOERBQUNDOzRDQUFJRCxXQUFXLENBQUMsZ0ZBQWdGLEVBQUVNLE1BQU1SLEtBQUssQ0FBQyw2REFBNkQsQ0FBQztzREFDM0ssNEVBQUNRLE1BQU1YLElBQUk7Z0RBQUNLLFdBQVU7Ozs7Ozs7Ozs7O3NEQUl4Qiw4REFBQ1E7NENBQUdSLFdBQVU7c0RBQ1hNLE1BQU1WLEtBQUs7Ozs7OztzREFFZCw4REFBQ1E7NENBQUVKLFdBQVU7c0RBQ1ZNLE1BQU1ULFdBQVc7Ozs7OztzREFJcEIsOERBQUNJOzRDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7OzJCQXJCWk87Ozs7Ozs7Ozs7OEJBNEJYLDhEQUFDTjtvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUdSLFdBQVU7O3dDQUFpRDtzREFDbkQsOERBQUNHOzRDQUFLSCxXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7OzhDQUU1Qyw4REFBQ0k7b0NBQUVKLFdBQVU7OENBQTZDOzs7Ozs7Ozs7Ozs7c0NBSzVELDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNHO2dEQUFLSCxXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7c0RBRWxELDhEQUFDUzs0Q0FBR1QsV0FBVTtzREFBMkM7Ozs7OztzREFDekQsOERBQUNJOzRDQUFFSixXQUFVO3NEQUEyQjs7Ozs7Ozs7Ozs7OzhDQUcxQyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ0c7Z0RBQUtILFdBQVU7MERBQWdDOzs7Ozs7Ozs7OztzREFFbEQsOERBQUNTOzRDQUFHVCxXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ0k7NENBQUVKLFdBQVU7c0RBQTJCOzs7Ozs7Ozs7Ozs7OENBRzFDLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDRztnREFBS0gsV0FBVTswREFBZ0M7Ozs7Ozs7Ozs7O3NEQUVsRCw4REFBQ1M7NENBQUdULFdBQVU7c0RBQTJDOzs7Ozs7c0RBQ3pELDhEQUFDSTs0Q0FBRUosV0FBVTtzREFBMkI7Ozs7Ozs7Ozs7Ozs4Q0FHMUMsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNHO2dEQUFLSCxXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7c0RBRWxELDhEQUFDUzs0Q0FBR1QsV0FBVTtzREFBMkM7Ozs7OztzREFDekQsOERBQUNJOzRDQUFFSixXQUFVO3NEQUEyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUk1Qyw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNVO2dDQUNDQyxNQUFLO2dDQUNMQyxRQUFPO2dDQUNQQyxLQUFJO2dDQUNKYixXQUFVOztrREFFViw4REFBQ0c7a0RBQUs7Ozs7OztrREFDTiw4REFBQ2QsK0dBQUlBO3dDQUFDVyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzlCO0FBRUEsaUVBQWVQLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYWdpLWpld2VsbGVycy13ZWJzaXRlLy4vY29tcG9uZW50cy9sZWdhY3kvVmFsdWVzLnRzeD84OGEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNoaWVsZCwgSGVhcnQsIFN0YXIsIFVzZXJzLCBBd2FyZCwgQ2xvY2sgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IFZhbHVlcyA9ICgpID0+IHtcbiAgY29uc3QgY29yZVZhbHVlcyA9IFtcbiAgICB7XG4gICAgICBpY29uOiBIZWFydCxcbiAgICAgIHRpdGxlOiAnQXV0aGVudGljaXR5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRXZlcnkgcGllY2UgaXMgZ2VudWluZSBhbmQgY3JhZnRlZCB3aXRoIGNhcmUuIFdlIGd1YXJhbnRlZSB0aGUgYXV0aGVudGljaXR5IG9mIGFsbCBvdXIgcHJvZHVjdHMuJyxcbiAgICAgIGNvbG9yOiAnZnJvbS1yZWQtNTAwIHRvLXBpbmstNTAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgdGl0bGU6ICdUcnVzdCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ0J1aWx0IG92ZXIgMjYgeWVhcnMgb2YgaG9uZXN0IHJlbGF0aW9uc2hpcHMgYW5kIHRyYW5zcGFyZW50IGJ1c2luZXNzIHByYWN0aWNlcy4nLFxuICAgICAgY29sb3I6ICdmcm9tLWJsdWUtNTAwIHRvLWluZGlnby01MDAnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBTdGFyLFxuICAgICAgdGl0bGU6ICdRdWFsaXR5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQklTIGNlcnRpZmllZCBwcm9kdWN0cyB0aGF0IG1lZXQgdGhlIGhpZ2hlc3Qgc3RhbmRhcmRzIG9mIGNyYWZ0c21hbnNoaXAgYW5kIGR1cmFiaWxpdHkuJyxcbiAgICAgIGNvbG9yOiAnZnJvbS1nb2xkLTUwMCB0by15ZWxsb3ctNTAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogVXNlcnMsXG4gICAgICB0aXRsZTogJ0ZhbWlseScsXG4gICAgICBkZXNjcmlwdGlvbjogJ1dlIHRyZWF0IGV2ZXJ5IGN1c3RvbWVyIGFzIGZhbWlseSwgcHJvdmlkaW5nIHBlcnNvbmFsaXplZCBzZXJ2aWNlIGFuZCBjYXJlLicsXG4gICAgICBjb2xvcjogJ2Zyb20tZ3JlZW4tNTAwIHRvLWVtZXJhbGQtNTAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogQXdhcmQsXG4gICAgICB0aXRsZTogJ0V4Y2VsbGVuY2UnLFxuICAgICAgZGVzY3JpcHRpb246ICdDb250aW51b3VzIHB1cnN1aXQgb2YgcGVyZmVjdGlvbiBpbiBkZXNpZ24sIHF1YWxpdHksIGFuZCBjdXN0b21lciBzYXRpc2ZhY3Rpb24uJyxcbiAgICAgIGNvbG9yOiAnZnJvbS1wdXJwbGUtNTAwIHRvLXZpb2xldC01MDAnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBDbG9jayxcbiAgICAgIHRpdGxlOiAnTGVnYWN5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRpbmcgdGltZWxlc3MgcGllY2VzIHRoYXQgYmVjb21lIGNoZXJpc2hlZCBoZWlybG9vbXMgZm9yIGZ1dHVyZSBnZW5lcmF0aW9ucy4nLFxuICAgICAgY29sb3I6ICdmcm9tLW9yYW5nZS01MDAgdG8tcmVkLTUwMCdcbiAgICB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInNlY3Rpb24tcGFkZGluZyBiZy13aGl0ZVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXItY3VzdG9tXCI+XG4gICAgICAgIHsvKiBTZWN0aW9uIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LXNlcmlmIGZvbnQtYm9sZCB0ZXh0LWVsZWdhbnQtODAwIG1iLTZcIj5cbiAgICAgICAgICAgIE91ciBDb3JlIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ29sZC02MDBcIj5WYWx1ZXM8L3NwYW4+XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZWxlZ2FudC02MDAgbWF4LXctM3hsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICBUaGVzZSBmdW5kYW1lbnRhbCBwcmluY2lwbGVzIGhhdmUgZ3VpZGVkIE5BR0kgSkVXRUxMRVJTIExURCBmb3Igb3ZlciAyNiB5ZWFycyBcbiAgICAgICAgICAgIGFuZCBjb250aW51ZSB0byBzaGFwZSBldmVyeSBhc3BlY3Qgb2Ygb3VyIGJ1c2luZXNzLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFZhbHVlcyBHcmlkICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTggbWItMTZcIj5cbiAgICAgICAgICB7Y29yZVZhbHVlcy5tYXAoKHZhbHVlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC0yeGwgYmctd2hpdGUgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHRyYW5zZm9ybSBob3ZlcjotdHJhbnNsYXRlLXktMlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIEdyYWRpZW50ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgJHt2YWx1ZS5jb2xvcn0gb3BhY2l0eS01IGdyb3VwLWhvdmVyOm9wYWNpdHktMTAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMGB9PjwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLThcIj5cbiAgICAgICAgICAgICAgICB7LyogSWNvbiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgJHt2YWx1ZS5jb2xvcn0gbWItNiBncm91cC1ob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwYH0+XG4gICAgICAgICAgICAgICAgICA8dmFsdWUuaWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtZWxlZ2FudC04MDAgbWItNCBncm91cC1ob3Zlcjp0ZXh0LWdvbGQtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAge3ZhbHVlLnRpdGxlfVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGVnYW50LTYwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgIHt2YWx1ZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICB7LyogRGVjb3JhdGl2ZSBFbGVtZW50ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCB3LTIwIGgtMjAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1nb2xkLTUwMC8xMCB0by10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYmx1ci14bCBncm91cC1ob3ZlcjpzY2FsZS0xNTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBWYWx1ZXMgaW4gQWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1lbGVnYW50LTgwMCB0by1lbGVnYW50LTkwMCByb3VuZGVkLTJ4bCBwLTggbWQ6cC0xMiB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtc2VyaWYgZm9udC1ib2xkIG1iLTRcIj5cbiAgICAgICAgICAgICAgVmFsdWVzIGluIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ29sZC00MDBcIj5BY3Rpb248L3NwYW4+XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWVsZWdhbnQtMzAwIG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIFNlZSBob3cgb3VyIGNvcmUgdmFsdWVzIHRyYW5zbGF0ZSBpbnRvIHJlYWwgYmVuZWZpdHMgZm9yIG91ciBjdXN0b21lcnNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdvbGQtNTAwIHctMTYgaC0xNiByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj4yNis8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTIgdGV4dC1nb2xkLTQwMFwiPlllYXJzIG9mIFRydXN0PC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGVnYW50LTMwMCB0ZXh0LXNtXCI+Q29uc2lzdGVudCBxdWFsaXR5IGFuZCBzZXJ2aWNlPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1nb2xkLTUwMCB3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+MTAwJTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMiB0ZXh0LWdvbGQtNDAwXCI+QXV0aGVudGljIFByb2R1Y3RzPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGVnYW50LTMwMCB0ZXh0LXNtXCI+R3VhcmFudGVlZCBnZW51aW5lIGpld2VsbGVyeTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ29sZC01MDAgdy0xNiBoLTE2IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPjEwSys8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTIgdGV4dC1nb2xkLTQwMFwiPkhhcHB5IEZhbWlsaWVzPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGVnYW50LTMwMCB0ZXh0LXNtXCI+U2F0aXNmaWVkIGN1c3RvbWVycyBuYXRpb253aWRlPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1nb2xkLTUwMCB3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+NC414piFPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yIHRleHQtZ29sZC00MDBcIj5BbWF6b24gUmF0aW5nPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbGVnYW50LTMwMCB0ZXh0LXNtXCI+Q29uc2lzdGVudGx5IGhpZ2ggcmV2aWV3czwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtdC0xMlwiPlxuICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vYW1hem9uLmluL3Nob3BzL25hZ2lqZXdlbGxlcnNcIlxuICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ29sZC01MDAgaG92ZXI6YmctZ29sZC02MDAgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHB5LTQgcHgtOCByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1IGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3Bhbj5FeHBlcmllbmNlIE91ciBWYWx1ZXM8L3NwYW4+XG4gICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBWYWx1ZXNcbiJdLCJuYW1lcyI6WyJTaGllbGQiLCJIZWFydCIsIlN0YXIiLCJVc2VycyIsIkF3YXJkIiwiQ2xvY2siLCJWYWx1ZXMiLCJjb3JlVmFsdWVzIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjb2xvciIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJkaXYiLCJoMiIsInNwYW4iLCJwIiwibWFwIiwidmFsdWUiLCJpbmRleCIsImgzIiwiaDQiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./components/legacy/Values.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flegacy%2Fpage&page=%2Flegacy%2Fpage&appPaths=%2Flegacy%2Fpage&pagePath=private-next-app-dir%2Flegacy%2Fpage.tsx&appDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNaman%20Nagi%5CDesktop%5CNAGI%20JEWELLERS&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();