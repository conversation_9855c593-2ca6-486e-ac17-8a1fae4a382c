"use strict";(()=>{var e={};e.id=703,e.ids=[703],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1781:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>c,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>h});var o={};a.r(o),a.d(o,{GET:()=>p});var r=a(5419),s=a(9108),n=a(9678),i=a(9647),u=a(7252);async function p(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/private/","/admin/"]},sitemap:"https://nagijewellers.com/sitemap.xml"},t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let l=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&isDynamic=1!C:\\Users\\<USER>\\Desktop\\NAGI JEWELLERS\\app\\robots.ts?__next_metadata_route__",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:m,serverHooks:c,headerHooks:x,staticGenerationBailout:h}=l,b="/robots.txt/route";function g(){return(0,n.patchFetch)({serverHooks:c,staticGenerationAsyncStorage:m})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[638,224],()=>a(1781));module.exports=o})();